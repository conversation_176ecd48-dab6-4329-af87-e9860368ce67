'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const isHomepage = pathname === '/';

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  const navLinks = [
    { href: '/', label: 'HOME' },
    { href: '/storia', label: 'STORIA' },
    { href: '/servizi', label: 'SERVIZI' },
    { href: '/occhiali', label: 'OCCHIALI DA VISTA' },
    { href: '/lenti-contatto', label: 'LENTI A CONTATTO' },
    { href: '/occhiali-sole', label: 'OCCHIALI DA SOLE' },
    { href: '/esami-vista', label: 'ESAMI DELLA VISTA' },
    { href: '/contatti', label: 'CONTATTI' },
  ];

  // Determine header background based on page and scroll state
  const getHeaderBackground = () => {
    if (isScrolled) {
      return 'bg-white shadow-lg';
    }
    if (isHomepage) {
      return 'bg-white shadow-sm';
    }
    return 'bg-white/90 backdrop-blur-sm shadow-sm';
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${getHeaderBackground()}`}
    >
      <nav className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="relative h-10 w-10 flex-shrink-0">
              <Image
                src="/images/logo/logo3.png"
                alt="Ottica GR1 Logo"
                fill
                className="object-contain"
                priority
              />
            </div>
            <div className="text-2xl font-bold font-sans">
              <span className="text-primary transition-colors duration-300">
                OTTICA
              </span>
              <span className="text-accent transition-colors duration-300 ml-1">
                GR1
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`font-sans text-sm font-medium transition-colors duration-300 hover:text-accent ${
                  pathname === link.href
                    ? 'text-primary'
                    : 'text-text-base hover:text-primary'
                }`}
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden flex flex-col space-y-1 w-6 h-6"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <span
              className={`block h-0.5 w-6 transition-all duration-300 bg-primary ${isMobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}
            />
            <span
              className={`block h-0.5 w-6 transition-all duration-300 bg-primary ${isMobileMenuOpen ? 'opacity-0' : ''}`}
            />
            <span
              className={`block h-0.5 w-6 transition-all duration-300 bg-primary ${isMobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}
            />
          </button>
        </div>

        {/* Mobile Sidebar Overlay */}
        {isMobileMenuOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-[55]"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}

        {/* Mobile Sidebar */}
        <div
          className={`lg:hidden fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-[60] transform transition-transform duration-300 ease-in-out ${
            isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
          }`}
        >
          <div className="flex flex-col h-full">
            {/* Sidebar Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="relative h-8 w-8 flex-shrink-0">
                  <Image
                    src="/images/logo/logo3.png"
                    alt="Ottica GR1 Logo"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="text-lg font-bold font-sans">
                  <span className="text-primary">OTTICA</span>
                  <span className="text-accent ml-1">GR1</span>
                </div>
              </div>
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                aria-label="Close menu"
              >
                <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Navigation Links */}
            <nav className="flex-1 px-6 py-6">
              <div className="space-y-2">
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`block font-sans text-base font-medium transition-all duration-200 py-3 px-4 rounded-lg ${
                      pathname === link.href
                        ? 'text-primary bg-primary/10 border-l-4 border-primary'
                        : 'text-text-base hover:text-primary hover:bg-primary/5 hover:translate-x-1'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {link.label}
                  </Link>
                ))}
              </div>
            </nav>

            {/* Sidebar Footer */}
            <div className="p-6 border-t border-gray-200">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">Dal 1982 a Montesacro</p>
                <p className="text-xs text-gray-500">Tradizione e innovazione</p>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Header;
